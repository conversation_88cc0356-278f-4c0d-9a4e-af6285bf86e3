import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import investmentEarningsService from '../services/investmentEarningsService';
import '../styles/ActiveInvestments.css';

const ActiveInvestments = () => {
  const { user } = useAuth();
  const [activeInvestments, setActiveInvestments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    loadActiveInvestments();
  }, [user]);

  const loadActiveInvestments = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError('');

      // Get the auth user ID from the user object
      const authId = user.id;
      console.log('Loading active investments for user:', authId);

      // Load active investments with detailed metrics
      const activeInvestmentsData = await investmentEarningsService.getUserActiveInvestments(authId);
      if (activeInvestmentsData) {
        console.log('Active Investments - Loaded investments:', activeInvestmentsData);
        setActiveInvestments(activeInvestmentsData);
      }
    } catch (error) {
      console.error('Error loading active investments:', error);
      setError('Failed to load active investments');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return '#22c55e';
      case 'completed':
        return '#3b82f6';
      case 'pending':
        return '#f59e0b';
      default:
        return '#6b7280';
    }
  };

  if (loading) {
    return (
      <div className="active-investments-page">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading your active investments...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="active-investments-page">
        <div className="error-container">
          <div className="error-icon">⚠️</div>
          <h3>Error Loading Investments</h3>
          <p>{error}</p>
          <button onClick={loadActiveInvestments} className="retry-btn">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="active-investments-page">
      <div className="page-header">
        <h1>Active Investments</h1>
        <p>Monitor and track your investment portfolio performance</p>
      </div>

      {activeInvestments.length === 0 ? (
        <div className="no-investments">
          <div className="no-investments-icon">📊</div>
          <h3>No Active Investments</h3>
          <p>You don't have any active investments yet. Start investing to see your portfolio here.</p>
          <a href="/app/invest" className="start-investing-btn">
            Start Investing
          </a>
        </div>
      ) : (
        <div className="investments-container">
          <div className="investments-summary">
            <div className="summary-card">
              <h3>Total Invested</h3>
              <div className="summary-value">
                {formatCurrency(activeInvestments.reduce((sum, inv) => sum + parseFloat(inv.amount), 0))}
              </div>
            </div>
            <div className="summary-card">
              <h3>Total Earned</h3>
              <div className="summary-value earned">
                {formatCurrency(activeInvestments.reduce((sum, inv) => sum + inv.currentReturn, 0))}
              </div>
            </div>
            <div className="summary-card">
              <h3>Active Plans</h3>
              <div className="summary-value">
                {activeInvestments.length}
              </div>
            </div>
            <div className="summary-card">
              <h3>Expected Total</h3>
              <div className="summary-value expected">
                {formatCurrency(activeInvestments.reduce((sum, inv) => sum + inv.expectedTotalReturn, 0))}
              </div>
            </div>
          </div>

          <div className="investments-grid">
            {activeInvestments.map((investment) => (
              <div key={investment.id} className="investment-detail-card">
                <div className="card-header">
                  <div className="plan-info">
                    <h3>{investment.investment_plans.name}</h3>
                    <div className="plan-details">
                      <span className="daily-return">{parseFloat(investment.investment_plans.daily_return)}% Daily</span>
                      <span className="total-return">{parseFloat(investment.investment_plans.total_return)}% Total</span>
                    </div>
                  </div>
                  <div className="investment-status">
                    <span 
                      className="status-indicator"
                      style={{ backgroundColor: getStatusColor(investment.status) }}
                    ></span>
                    <span className="status-text">{investment.status}</span>
                  </div>
                </div>

                <div className="card-body">
                  <div className="investment-metrics">
                    <div className="metric-row">
                      <div className="metric">
                        <span className="metric-label">Initial Investment</span>
                        <span className="metric-value">{formatCurrency(parseFloat(investment.amount))}</span>
                      </div>
                      <div className="metric">
                        <span className="metric-label">Current Earnings</span>
                        <span className="metric-value earned">{formatCurrency(investment.currentReturn)}</span>
                      </div>
                    </div>
                    
                    <div className="metric-row">
                      <div className="metric">
                        <span className="metric-label">Expected Total</span>
                        <span className="metric-value expected">{formatCurrency(investment.expectedTotalReturn)}</span>
                      </div>
                      <div className="metric">
                        <span className="metric-label">Remaining Days</span>
                        <span className="metric-value">{investment.remainingDays} days</span>
                      </div>
                    </div>
                  </div>

                  <div className="progress-section">
                    <div className="progress-header">
                      <span className="progress-label">Investment Progress</span>
                      <span className="progress-percentage">{investment.progressPercentage}%</span>
                    </div>
                    <div className="progress-bar">
                      <div 
                        className="progress-fill"
                        style={{ width: `${investment.progressPercentage}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="investment-timeline">
                    <div className="timeline-item">
                      <span className="timeline-label">Started</span>
                      <span className="timeline-value">{formatDate(investment.created_at)}</span>
                    </div>
                    <div className="timeline-item">
                      <span className="timeline-label">Duration</span>
                      <span className="timeline-value">{investment.investment_plans.duration_days} days</span>
                    </div>
                    <div className="timeline-item">
                      <span className="timeline-label">Expected End</span>
                      <span className="timeline-value">
                        {formatDate(new Date(new Date(investment.created_at).getTime() + 
                          investment.investment_plans.duration_days * 24 * 60 * 60 * 1000))}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ActiveInvestments;
