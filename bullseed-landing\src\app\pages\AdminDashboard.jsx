import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import adminAuthService from '../../services/adminAuthService.js';
import bitcoinService from '../../services/bitcoinService.js';
import ethereumService from '../../services/ethereumService.js';
import bnbService from '../../services/bnbService.js';
import { supabase } from '../../lib/supabase.js';
import { adminDbService } from '../../lib/supabaseAdmin.js';
import '../styles/AdminDashboard.css';

const AdminDashboard = () => {
  const [admin, setAdmin] = useState(null);
  const [loading, setLoading] = useState(true);
  const [deposits, setDeposits] = useState([]);
  const [bitcoinInfo, setBitcoinInfo] = useState(null);
  const [ethereumInfo, setEthereumInfo] = useState(null);
  const [bnbInfo, setBnbInfo] = useState(null);
  const [recentTransactions, setRecentTransactions] = useState([]);
  const [selectedDeposit, setSelectedDeposit] = useState(null);
  const [confirmingDeposit, setConfirmingDeposit] = useState(null);
  const [confirmModal, setConfirmModal] = useState(null);
  const [alertModal, setAlertModal] = useState(null);
  const [settingsModal, setSettingsModal] = useState(false);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [changingPassword, setChangingPassword] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const initializeAdmin = async () => {
      await checkAuth();
      await loadDashboardData();

      // Request notification permission
      if ('Notification' in window && Notification.permission === 'default') {
        await Notification.requestPermission();
      }
    };

    initializeAdmin();

    // Set up real-time subscription for deposits
    const subscription = supabase
      .channel('admin_deposits_channel')
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'crypto_deposits'
        },
        (payload) => {
          console.log('Admin - Deposit change detected:', payload);

          // Show notification for new deposits (correct property name is 'eventType')
          if (payload.eventType === 'INSERT' && payload.new) {
            showNotification('New Deposit Request', `New deposit of $${payload.new.amount_usd} received!`);
            playNotificationSound();
          }

          // Show notification for status changes
          if (payload.eventType === 'UPDATE' && payload.old && payload.new) {
            if (payload.old.status !== payload.new.status) {
              showNotification('Deposit Status Changed', `Deposit status changed to ${payload.new.status}`);
            }
          }

          // Always reload deposits for any change
          console.log('Reloading deposits due to change...');
          setTimeout(() => loadDeposits(), 100);
        }
      )
      .subscribe((status, err) => {
        console.log('Admin subscription status:', status);
        if (err) {
          console.error('Admin subscription error:', err);
        }
        if (status === 'SUBSCRIBED') {
          console.log('✅ Admin real-time subscription is active');
        }
      });

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, []);

  // Notification functions
  const showNotification = (title, message) => {
    console.log('Showing notification:', title, message);

    // Browser notification
    if ('Notification' in window) {
      if (Notification.permission === 'granted') {
        const notification = new Notification(title, {
          body: message,
          icon: '/favicon.ico',
          badge: '/favicon.ico',
          tag: 'bullseed-admin'
        });

        // Auto close after 5 seconds
        setTimeout(() => notification.close(), 5000);
      } else if (Notification.permission === 'default') {
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            showNotification(title, message);
          }
        });
      }
    }

    // Also show in-app alert
    setAlertModal({
      title: title,
      message: message,
      type: 'info',
      onClose: () => setAlertModal(null)
    });
  };

  const playNotificationSound = () => {
    // Create a simple notification sound
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.value = 800;
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.5);
  };

  // Request notification permission on component mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  // PWA Install prompt handling
  useEffect(() => {
    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault();
      setDeferredPrompt(e);
      setShowInstallPrompt(true);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    deferredPrompt.prompt();
    const { outcome } = await deferredPrompt.userChoice;

    if (outcome === 'accepted') {
      console.log('User accepted the install prompt');
    } else {
      console.log('User dismissed the install prompt');
    }

    setDeferredPrompt(null);
    setShowInstallPrompt(false);
  };

  const checkAuth = async () => {
    const isAuth = await adminAuthService.isAuthenticated();
    if (!isAuth) {
      navigate('/app/admin/login');
      return;
    }

    const currentAdmin = await adminAuthService.getCurrentAdmin();
    setAdmin(currentAdmin);
  };

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadDeposits(),
        loadBitcoinInfo(),
        loadEthereumInfo(),
        loadBnbInfo(),
        loadRecentTransactions()
      ]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadDeposits = async () => {
    try {
      const data = await adminDbService.getAllDeposits(50);
      setDeposits(data || []);
    } catch (error) {
      console.error('Error loading deposits:', error);
    }
  };

  const loadBitcoinInfo = async () => {
    try {
      const info = await bitcoinService.getAddressInfo();
      setBitcoinInfo(info);
    } catch (error) {
      console.error('Error loading Bitcoin info:', error);
    }
  };

  const loadEthereumInfo = async () => {
    try {
      const info = await ethereumService.getAddressInfo();
      setEthereumInfo(info);
    } catch (error) {
      console.error('Error loading Ethereum info:', error);
    }
  };

  const loadBnbInfo = async () => {
    try {
      const info = await bnbService.getAddressInfo();
      setBnbInfo(info);
    } catch (error) {
      console.error('Error loading BNB info:', error);
    }
  };

  const loadRecentTransactions = async () => {
    try {
      const transactions = await bitcoinService.getIncomingTransactions(10);
      setRecentTransactions(transactions);
    } catch (error) {
      console.error('Error loading recent transactions:', error);
    }
  };

  const handleConfirmDeposit = async (deposit) => {
    setConfirmModal({
      title: 'Confirm Deposit',
      message: `Confirm deposit of $${deposit.amount_usd} for ${deposit.users?.name || 'Unknown User'}?`,
      onConfirm: () => processDepositConfirmation(deposit),
      onCancel: () => setConfirmModal(null)
    });
  };

  const processDepositConfirmation = async (deposit) => {
    setConfirmModal(null);
    setConfirmingDeposit(deposit.id);

    try {
      // Use admin service to confirm deposit (bypasses RLS)
      await adminDbService.confirmDeposit(
        deposit.id,
        deposit.amount_usd,
        deposit.user_id
      );

      setAlertModal({
        title: 'Success!',
        message: 'Deposit confirmed successfully!',
        type: 'success',
        onClose: () => setAlertModal(null)
      });
      loadDeposits(); // Reload deposits

    } catch (error) {
      console.error('Error confirming deposit:', error);
      setAlertModal({
        title: 'Error',
        message: 'Error confirming deposit: ' + error.message,
        type: 'error',
        onClose: () => setAlertModal(null)
      });
    } finally {
      setConfirmingDeposit(null);
    }
  };

  const handleRejectDeposit = async (deposit) => {
    setConfirmModal({
      title: 'Reject Deposit',
      message: `Reject deposit of $${deposit.amount_usd} for ${deposit.users?.name || 'Unknown User'}?`,
      onConfirm: () => processDepositRejection(deposit),
      onCancel: () => setConfirmModal(null),
      type: 'danger'
    });
  };

  const processDepositRejection = async (deposit) => {
    setConfirmModal(null);

    try {
      // Use admin service to reject deposit
      await adminDbService.rejectDeposit(deposit.id);

      setAlertModal({
        title: 'Deposit Rejected',
        message: 'Deposit has been rejected.',
        type: 'warning',
        onClose: () => setAlertModal(null)
      });
      loadDeposits();

    } catch (error) {
      console.error('Error rejecting deposit:', error);
      setAlertModal({
        title: 'Error',
        message: 'Error rejecting deposit: ' + error.message,
        type: 'error',
        onClose: () => setAlertModal(null)
      });
    }
  };

  const handleLogout = async () => {
    await adminAuthService.logout();
    navigate('/app/admin/login');
  };

  const handlePasswordChange = async (e) => {
    e.preventDefault();

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setAlertModal({
        title: 'Error',
        message: 'New passwords do not match.',
        type: 'error',
        onClose: () => setAlertModal(null)
      });
      return;
    }

    if (passwordForm.newPassword.length < 8) {
      setAlertModal({
        title: 'Error',
        message: 'Password must be at least 8 characters long.',
        type: 'error',
        onClose: () => setAlertModal(null)
      });
      return;
    }

    setChangingPassword(true);

    try {
      const result = await adminAuthService.changePassword(
        passwordForm.currentPassword,
        passwordForm.newPassword
      );

      if (result.success) {
        setAlertModal({
          title: 'Success!',
          message: 'Password changed successfully!',
          type: 'success',
          onClose: () => setAlertModal(null)
        });
        setSettingsModal(false);
        setPasswordForm({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      } else {
        setAlertModal({
          title: 'Error',
          message: result.error || 'Failed to change password.',
          type: 'error',
          onClose: () => setAlertModal(null)
        });
      }
    } catch (error) {
      setAlertModal({
        title: 'Error',
        message: 'An error occurred while changing password.',
        type: 'error',
        onClose: () => setAlertModal(null)
      });
    } finally {
      setChangingPassword(false);
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { color: '#ffc107', bg: 'rgba(255, 193, 7, 0.1)', text: 'Pending' },
      detected: { color: '#007bff', bg: 'rgba(0, 123, 255, 0.1)', text: 'Detected' },
      confirmed: { color: '#28a745', bg: 'rgba(40, 167, 69, 0.1)', text: 'Confirmed' },
      failed: { color: '#dc3545', bg: 'rgba(220, 53, 69, 0.1)', text: 'Failed' },
      expired: { color: '#6c757d', bg: 'rgba(108, 117, 125, 0.1)', text: 'Expired' }
    };

    const config = statusConfig[status] || statusConfig.pending;

    return (
      <span 
        className="status-badge"
        style={{ 
          color: config.color, 
          backgroundColor: config.bg,
          border: `1px solid ${config.color}30`
        }}
      >
        {config.text}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const formatBTC = (amount) => {
    if (amount === null || amount === undefined || isNaN(amount)) return '0.00000000';
    return parseFloat(amount).toFixed(8);
  };

  const formatCryptoAmount = (amount, cryptocurrency) => {
    const decimals = cryptocurrency === 'BTC' ? 8 : 6;
    return parseFloat(amount).toFixed(decimals);
  };

  const formatETH = (amount) => {
    if (amount === null || amount === undefined || isNaN(amount)) return '0.000000';
    return parseFloat(amount).toFixed(6);
  };

  const formatBNB = (amount) => {
    if (amount === null || amount === undefined || isNaN(amount)) return '0.000000';
    return parseFloat(amount).toFixed(6);
  };

  if (loading) {
    return (
      <div className="admin-loading">
        <div className="admin-loading-spinner"></div>
        <p>Loading admin dashboard...</p>
      </div>
    );
  }

  return (
    <div className="admin-dashboard">
      <div className="admin-header">
        <div className="admin-header-left">
          <h1>🔐 BullSeed Admin Dashboard</h1>
          <p>Welcome back, {admin?.username}</p>
        </div>
        <div className="admin-header-right">
          <button onClick={loadDashboardData} className="admin-refresh-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="23,4 23,10 17,10"/>
              <polyline points="1,20 1,14 7,14"/>
              <path d="M20.49,9A9,9,0,0,0,5.64,5.64L1,10m22,4L18.36,18.36A9,9,0,0,1,3.51,15"/>
            </svg>
            Refresh
          </button>
          <button onClick={() => setSettingsModal(true)} className="admin-settings-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="3"/>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
            </svg>
            Settings
          </button>
          <button onClick={handleLogout} className="admin-logout-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
              <polyline points="16,17 21,12 16,7"/>
              <line x1="21" y1="12" x2="9" y2="12"/>
            </svg>
            Logout
          </button>
        </div>
      </div>

      <div className="admin-stats">
        <div className="admin-stat-card">
          <div className="admin-stat-icon bitcoin">₿</div>
          <div className="admin-stat-info">
            <h3>Bitcoin Address</h3>
            <p className="bitcoin-address">******************************************</p>
            {bitcoinInfo && (
              <div className="bitcoin-stats">
                <span>Balance: {formatBTC(bitcoinInfo.balance)} BTC</span>
                <span>Total Received: {formatBTC(bitcoinInfo.total_received)} BTC</span>
                <span>Transactions: {bitcoinInfo.n_tx}</span>
              </div>
            )}
          </div>
        </div>

        <div className="admin-stat-card">
          <div className="admin-stat-icon ethereum">⟠</div>
          <div className="admin-stat-info">
            <h3>Ethereum Address</h3>
            <p className="crypto-address">******************************************</p>
            {ethereumInfo && (
              <div className="crypto-stats">
                <span>Balance: {formatETH(ethereumInfo.balance)} ETH</span>
                <span>Total Received: {formatETH(ethereumInfo.total_received)} ETH</span>
                <span>Transactions: {ethereumInfo.n_tx}</span>
              </div>
            )}
          </div>
        </div>

        <div className="admin-stat-card">
          <div className="admin-stat-icon bnb">◆</div>
          <div className="admin-stat-info">
            <h3>BNB Smart Chain Address</h3>
            <p className="crypto-address">******************************************</p>
            {bnbInfo && (
              <div className="crypto-stats">
                <span>Balance: {formatBNB(bnbInfo.balance)} BNB</span>
                <span>Total Received: {formatBNB(bnbInfo.total_received)} BNB</span>
                <span>Transactions: {bnbInfo.n_tx}</span>
              </div>
            )}
          </div>
        </div>

        <div className="admin-stat-card">
          <div className="admin-stat-icon deposits">📥</div>
          <div className="admin-stat-info">
            <h3>Pending Deposits</h3>
            <p className="stat-number">{deposits.filter(d => d.status === 'pending').length}</p>
            <span>Awaiting confirmation</span>
          </div>
        </div>

        <div className="admin-stat-card">
          <div className="admin-stat-icon confirmed">✅</div>
          <div className="admin-stat-info">
            <h3>Confirmed Today</h3>
            <p className="stat-number">
              {deposits.filter(d =>
                d.status === 'confirmed' &&
                new Date(d.confirmed_at).toDateString() === new Date().toDateString()
              ).length}
            </p>
            <span>Processed deposits</span>
          </div>
        </div>
      </div>

      {/* PWA Install Prompt */}
      {showInstallPrompt && (
        <div className="install-prompt">
          <div className="install-prompt-content">
            <div className="install-prompt-icon">📱</div>
            <div className="install-prompt-text">
              <h3>Install BullSeed Admin</h3>
              <p>Install the admin portal on your device for quick access and notifications</p>
            </div>
            <div className="install-prompt-actions">
              <button onClick={handleInstallClick} className="install-btn">
                Install
              </button>
              <button onClick={() => setShowInstallPrompt(false)} className="dismiss-btn">
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="admin-content">
        <div className="admin-deposits-section">
          <div className="section-header">
            <h2>💰 Deposit Management</h2>
            <p>Review and confirm user deposits</p>
          </div>

          <div className="deposits-table-container">
            <table className="deposits-table">
              <thead>
                <tr>
                  <th>User</th>
                  <th>Amount</th>
                  <th>Crypto Amount</th>
                  <th>Status</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {deposits.map(deposit => (
                  <tr key={deposit.id} className={`deposit-row ${deposit.status}`}>
                    <td>
                      <div className="user-info">
                        <strong>{deposit.users?.name || 'Unknown'}</strong>
                        <small>{deposit.users?.email || 'No email'}</small>
                      </div>
                    </td>
                    <td>
                      <span className="amount-usd">${deposit.amount_usd}</span>
                    </td>
                    <td>
                      <span className="amount-crypto">
                        {formatCryptoAmount(deposit.crypto_amount, deposit.cryptocurrency)} {deposit.cryptocurrency}
                      </span>
                    </td>
                    <td>
                      {getStatusBadge(deposit.status)}
                    </td>
                    <td>
                      <span className="date">{formatDate(deposit.created_at)}</span>
                    </td>
                    <td>
                      <div className="deposit-actions">
                        {deposit.status === 'pending' && (
                          <>
                            <button
                              onClick={() => handleConfirmDeposit(deposit)}
                              className="action-btn confirm"
                              disabled={confirmingDeposit === deposit.id}
                            >
                              {confirmingDeposit === deposit.id ? '...' : '✅'}
                            </button>
                            <button
                              onClick={() => handleRejectDeposit(deposit)}
                              className="action-btn reject"
                            >
                              ❌
                            </button>
                          </>
                        )}
                        <button
                          onClick={() => setSelectedDeposit(deposit)}
                          className="action-btn details"
                        >
                          👁️
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {deposits.length === 0 && (
              <div className="no-deposits">
                <p>No deposits found</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Deposit Details Modal */}
      {selectedDeposit && (
        <div className="modal-overlay" onClick={() => setSelectedDeposit(null)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Deposit Details</h3>
              <button onClick={() => setSelectedDeposit(null)} className="modal-close">×</button>
            </div>
            <div className="modal-body">
              <div className="deposit-detail-grid">
                <div className="detail-item">
                  <label>User:</label>
                  <span>{selectedDeposit.users?.name || 'Unknown'}</span>
                </div>
                <div className="detail-item">
                  <label>Email:</label>
                  <span>{selectedDeposit.users?.email || 'No email'}</span>
                </div>
                <div className="detail-item">
                  <label>USD Amount:</label>
                  <span>${selectedDeposit.amount_usd}</span>
                </div>
                <div className="detail-item">
                  <label>Crypto Amount:</label>
                  <span>
                    {formatCryptoAmount(selectedDeposit.crypto_amount, selectedDeposit.cryptocurrency)} {selectedDeposit.cryptocurrency}
                  </span>
                </div>
                <div className="detail-item">
                  <label>Status:</label>
                  {getStatusBadge(selectedDeposit.status)}
                </div>
                <div className="detail-item">
                  <label>Created:</label>
                  <span>{formatDate(selectedDeposit.created_at)}</span>
                </div>
                <div className="detail-item">
                  <label>Deposit Address:</label>
                  <span className="crypto-address">{selectedDeposit.deposit_address}</span>
                </div>
                {selectedDeposit.transaction_hash && (
                  <div className="detail-item">
                    <label>Transaction Hash:</label>
                    <span className="tx-hash">{selectedDeposit.transaction_hash}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Custom Confirmation Modal */}
      {confirmModal && (
        <div className="modal-overlay" onClick={confirmModal.onCancel}>
          <div className="confirm-modal" onClick={(e) => e.stopPropagation()}>
            <div className="confirm-modal-header">
              <h3>{confirmModal.title}</h3>
            </div>
            <div className="confirm-modal-body">
              <p>{confirmModal.message}</p>
            </div>
            <div className="confirm-modal-actions">
              <button
                onClick={confirmModal.onCancel}
                className="confirm-btn cancel"
              >
                Cancel
              </button>
              <button
                onClick={confirmModal.onConfirm}
                className={`confirm-btn ${confirmModal.type === 'danger' ? 'danger' : 'primary'}`}
              >
                {confirmModal.type === 'danger' ? 'Reject' : 'Confirm'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Custom Alert Modal */}
      {alertModal && (
        <div className="modal-overlay" onClick={alertModal.onClose}>
          <div className="alert-modal" onClick={(e) => e.stopPropagation()}>
            <div className={`alert-modal-header ${alertModal.type}`}>
              <div className="alert-icon">
                {alertModal.type === 'success' && '✅'}
                {alertModal.type === 'error' && '❌'}
                {alertModal.type === 'warning' && '⚠️'}
              </div>
              <h3>{alertModal.title}</h3>
            </div>
            <div className="alert-modal-body">
              <p>{alertModal.message}</p>
            </div>
            <div className="alert-modal-actions">
              <button
                onClick={alertModal.onClose}
                className="alert-btn primary"
              >
                OK
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Settings Modal */}
      {settingsModal && (
        <div className="modal-overlay" onClick={() => setSettingsModal(false)}>
          <div className="settings-modal" onClick={(e) => e.stopPropagation()}>
            <div className="settings-modal-header">
              <h3>⚙️ Admin Settings</h3>
              <button
                onClick={() => setSettingsModal(false)}
                className="close-btn"
              >
                ✕
              </button>
            </div>
            <div className="settings-modal-body">
              <div className="settings-section">
                <h4>🔐 Change Password</h4>
                <p className="security-warning">
                  ⚠️ <strong>Security Notice:</strong> The default password is exposed in the login page.
                  Please change it immediately for security.
                </p>
                <form onSubmit={handlePasswordChange} className="password-form">
                  <div className="form-group">
                    <label>Current Password</label>
                    <input
                      type="password"
                      value={passwordForm.currentPassword}
                      onChange={(e) => setPasswordForm({...passwordForm, currentPassword: e.target.value})}
                      required
                      placeholder="Enter current password"
                    />
                  </div>
                  <div className="form-group">
                    <label>New Password</label>
                    <input
                      type="password"
                      value={passwordForm.newPassword}
                      onChange={(e) => setPasswordForm({...passwordForm, newPassword: e.target.value})}
                      required
                      placeholder="Enter new password (min 8 characters)"
                      minLength="8"
                    />
                  </div>
                  <div className="form-group">
                    <label>Confirm New Password</label>
                    <input
                      type="password"
                      value={passwordForm.confirmPassword}
                      onChange={(e) => setPasswordForm({...passwordForm, confirmPassword: e.target.value})}
                      required
                      placeholder="Confirm new password"
                    />
                  </div>
                  <div className="form-actions">
                    <button
                      type="button"
                      onClick={() => setSettingsModal(false)}
                      className="cancel-btn"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="save-btn"
                      disabled={changingPassword}
                    >
                      {changingPassword ? 'Changing...' : 'Change Password'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
