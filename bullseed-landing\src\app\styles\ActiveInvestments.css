.active-investments-page {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

.page-header {
  margin-bottom: 32px;
  text-align: center;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.page-header p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-container h3 {
  color: white;
  font-size: 20px;
  margin-bottom: 8px;
}

.error-container p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 24px;
}

.retry-btn {
  background: #00d4aa;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #00b894;
  transform: translateY(-2px);
}

/* No Investments State */
.no-investments {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 40px;
}

.no-investments-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.7;
}

.no-investments h3 {
  color: white;
  font-size: 24px;
  margin-bottom: 12px;
}

.no-investments p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 32px;
  max-width: 400px;
}

.start-investing-btn {
  background: linear-gradient(135deg, #00d4aa, #22c55e);
  color: white;
  text-decoration: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  display: inline-block;
}

.start-investing-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 212, 170, 0.3);
}

/* Investments Container */
.investments-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Summary Cards */
.investments-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 8px;
}

.summary-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.summary-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.summary-card h3 {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-value {
  color: white;
  font-size: 28px;
  font-weight: 700;
}

.summary-value.earned {
  color: #22c55e;
}

.summary-value.expected {
  color: #3b82f6;
}

/* Investment Detail Cards */
.investments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.investment-detail-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.investment-detail-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: #00d4aa;
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 212, 170, 0.1);
}

/* Card Header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.plan-info h3 {
  color: white;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 8px;
}

.plan-details {
  display: flex;
  gap: 12px;
}

.daily-return,
.total-return {
  background: rgba(0, 212, 170, 0.1);
  color: #00d4aa;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

.investment-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
}

/* Card Body */
.card-body {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.investment-metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.metric-value.earned {
  color: #22c55e;
}

.metric-value.expected {
  color: #3b82f6;
}

/* Progress Section */
.progress-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.progress-percentage {
  color: #00d4aa;
  font-size: 14px;
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00d4aa, #22c55e);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Timeline */
.investment-timeline {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.timeline-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.timeline-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.timeline-value {
  color: white;
  font-size: 14px;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .active-investments-page {
    padding: 20px;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .investments-summary {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .investments-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .investment-detail-card {
    padding: 20px;
  }

  .metric-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .active-investments-page {
    padding: 16px;
  }

  .investments-summary {
    grid-template-columns: 1fr;
  }

  .summary-value {
    font-size: 24px;
  }
}
